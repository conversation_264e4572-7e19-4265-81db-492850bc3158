name: Continuous Integration & Deployment

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main
jobs:
  build_trade_depot_web:
    name: Build Procurement Flutter Home Page
    runs-on: ubuntu-latest
    environment:
      name: prod
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.32.4'
      - name: Run dart env
        run: dart tool/env.dart
        env:
          INTERCOM_APP_ID: u2vxi1gf
          FLUTTER_APP_FLAVOR: prod
          FIREBASE_SERVICE_URL: 'https://us-central1-tradedepot-retail-167113.cloudfunctions.net'
          CONSOLE_URL: "https://console.tradedepot.co"
          APP_URL: "https://app.tradedepot.co"
          AWS_API_URL: "https://qn6x4esklh.execute-api.us-east-1.amazonaws.com/prod"
          AWS_API_URL_V2: "https://d8w0snbim6.execute-api.us-east-1.amazonaws.com/prod"
          AWS_API_URL_V3: "https://3pu9c0uvcc.execute-api.us-east-1.amazonaws.com/prod"
          AWS_API_URL_V4: "https://ryv8zgxumb.execute-api.us-east-1.amazonaws.com/prod"
          SEARCH_URL: "https://8mgz9m8xt2.execute-api.us-east-1.amazonaws.com/prod"
          SENTRY_DSN: "https://<EMAIL>/4508262252412928"
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_MAP_KEY }}
          HUBSPOT_API_KEY: ${{ secrets.HUBSPOT_API_KEY }}
      - name: Run Web env
        run: . ./parse-tmp-env.sh ./web/index.example.html ./web/index.html
        env:
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_MAP_KEY }}
          INTERCOM_APP_ID: u2vxi1gf
      # - run: flutter upgrade
      - run: git config --global url."https://${{ secrets.GIT_USER }}:${{ secrets.GIT_TOKEN }}@github.com/".insteadOf https://github.com/
      - run: flutter pub get
      - run: flutter test
      - run: flutter config --enable-web
      - run: flutter build web --release --source-maps #--web-renderer canvaskit
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
      - name: Deploy static site to S3 bucket
        env:
          S3_BUCKET_NAME: td-prod-procurement
          S3_CACHE_MAX_AGE: ${{ secrets.S3_CACHE_MAX_AGE }}
        run: |
          aws s3 sync build/web s3://$S3_BUCKET_NAME --delete
          aws s3 cp s3://$S3_BUCKET_NAME/ s3://$S3_BUCKET_NAME/ --metadata-directive REPLACE \
          --exclude "*" --include "*.jpg" --include "*.gif" --include "*.png" \
          --recursive --cache-control max-age=$S3_CACHE_MAX_AGE
      - name: Invalidate Cloud front distribution
        env:
          CLOUD_FRONT_DISTRIBUTION: E1CXL42PLCK2F8
        run: aws cloudfront create-invalidation --distribution-id $CLOUD_FRONT_DISTRIBUTION --paths "/*"
